<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 26 11:15:27 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-26 11:15:27,345</td>
<td class="Message">【websocket消息】连接断开，总数为:4</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">45</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-26 11:15:27,390</td>
<td class="Message">【websocket消息】连接断开，总数为:1</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">45</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-26 11:15:27,345</td>
<td class="Message">【websocket消息】连接断开，总数为:3</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">45</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-26 11:15:27,345</td>
<td class="Message">【websocket消息】连接断开，总数为:2</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">45</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-26 11:15:27,465</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">45</td>
</tr>
</table>
</body></html>