<template>
  <a-card size="small">
    <a-button  type="primary" slot="extra">
      <a :href="'#/sapsys/CsrGxjfProjectPm?&theme=' + projectInfo.projectName + '&projectId=' + projectInfo.projectId" target="_blank"> 项目移交 </a></a-button
    >
    <a-table
      style="margin-bottom: 10px"
      ref="table"
      size="small"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="false"
      :loading="loading"
      :scroll="{ y: 280, x: 600 }"
    >
      <span slot="action" slot-scope="text, record">
        <a v-if="isEdit && editId == record.id" @click="handleSave">保存</a>
        <a v-else @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a v-if="isEdit && editId == record.id" @click="handleCancel">取消</a>
        <a-popconfirm v-else title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </span>

      <span slot="system" slot-scope="text, record">
        <a-select
          v-if="isEdit && editId == record.id"
          v-model="editItem.systemId"
          style="width: 100%"
        >
          <a-select-option :value="0">Select System</a-select-option>
          <a-select-option v-for="(item, index) in subSystemList" :key="index" :value="item.id">{{
            item.systemName
          }}</a-select-option>
        </a-select>
        <span v-else>{{ record.systemName }}</span>
      </span>

      <span slot="remark" slot-scope="text, record">
        <a-input
          v-if="isEdit && editId == record.id"
          v-model="editItem.remark"
          style="width: 100%"
        />
        <span v-else>{{ record.remark }}</span>
      </span>

      <template v-for="(item, index) in editList" :slot="item" slot-scope="text, record">
        <div :key="index">
          <a-input-number
            v-if="isEdit && editId == record.id"
            :min="0"
            :max="100"
            :step="1"
            v-model="editItem[String(item)]"
          />
            <!-- :stroke-color="'#1890ff'"
            class="blue-text-progress" -->
            <a-progress
            v-else
            width="50px"
            type="circle" 
            :percent="Number(text)"
            :format="(percent) => `${percent}%`"
          />
        </div>
      </template>
    </a-table>
    <a-select v-model="systemId" style="width: 320px">
      <a-select-option :value="0">Select System</a-select-option>
      <a-select-option v-for="(item, index) in subSystemList" :key="index" :value="item.id">{{
        item.systemName
      }}</a-select-option>
    </a-select>
    <a-button style="margin-left: 10px" type="primary" @click="addSubSystem">Add</a-button>
  </a-card>
</template>

<script>
import { DecideListMixin } from "@/mixins/DecideListMixin";
import { getAction, postAction, putAction } from "@/api/manage";

export default {
  name: "SiteProgressList",
  mixins: [DecideListMixin],
  components: {},
  props: ["projectId", "projectInfo"],
  data() {
    return {
      description: "未备注管理页面",
      queryParam: {
        projectId: this.projectId,
      },
      // "trunkingWork","conduitWork","cableWork","cableTesting","pointChecking"
      editList: [
        "systemDesign",
        "drawingSubmission",
        "equipmentSubmission",
        "supplierEvaluation",
        "issuePO",
        "equipmentDelivery",
        ,
        "equipmentInstallation",
        "systemChecking",
        "testing",
        "systemHandover",
        "dlpCommenced",
      ],
      editId: 0,
      editItem: {},
      isEdit: false,
      subSystemList: [],
      systemId: 0,
      // 表头
      columns: [
        {
          title: "System",
          align: "center",
          key: "system",
          children: [
            {
              title: "操作",
              dataIndex: "action",
              align: "center",
              width: 100,
              scopedSlots: { customRender: "action" },
            },
            {
              title: "System",
              width: 180,
              align: "center",
              ellipsis: true,
              dataIndex: "systemName",
              scopedSlots: { customRender: "system" },
            },
          ],
        },
        {
          title: "Scope",
          align: "center",
          key: "scope",
          children: [
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  1.System design
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "systemDesign",
              scopedSlots: { customRender: "systemDesign" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  2.Drawing submission
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "drawingSubmission",
              scopedSlots: { customRender: "drawingSubmission" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  3.Equipment submission
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "equipmentSubmission",
              scopedSlots: { customRender: "equipmentSubmission" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  4.Supplier evaluation
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "supplierEvaluation",
              scopedSlots: { customRender: "supplierEvaluation" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  5.Issue PO
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "issuePO",
              scopedSlots: { customRender: "issuePO" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  6.Equipment delivery
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "equipmentDelivery",
              scopedSlots: { customRender: "equipmentDelivery" },
            },
            // {
            //   title: 'Trunking work',
            //   width: 100,
            //   align: 'center',
            //   dataIndex: 'trunkingWork',
            //   scopedSlots: { customRender: 'trunkingWork' },
            // },
            // {
            //   title: 'Conduit work',
            //   width: 100,
            //   align: 'center',
            //   dataIndex: 'conduitWork',
            //   scopedSlots: { customRender: 'conduitWork' },
            // },
            // {
            //   title: 'Cable work',
            //   width: 100,
            //   align: 'center',
            //   dataIndex: 'cableWork',
            //   scopedSlots: { customRender: 'cableWork' },
            // },
            // {
            //   title: 'Cable testing',
            //   width: 100,
            //   align: 'center',
            //   dataIndex: 'cableTesting',
            //   scopedSlots: { customRender: 'cableTesting' },
            // },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  7.Installation
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "equipmentInstallation",
              scopedSlots: { customRender: "equipmentInstallation" },
            },
            // {
            //   title: 'Point checking',
            //   width: 100,
            //   align: 'center',
            //   dataIndex: 'pointChecking',
            //   scopedSlots: { customRender: 'pointChecking' },
            // },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  8.System checking
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "systemChecking",
              scopedSlots: { customRender: "systemChecking" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  9.Testing
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "testing",
              scopedSlots: { customRender: "testing" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  10.System handover
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "systemHandover",
              scopedSlots: { customRender: "systemHandover" },
            },
            {
              title: () => (
                <span style="display: flex; align-items: center; justify-content: center;">
                  11.DLP commenced
                  <a-icon
                    type="arrow-right"
                    style="margin-left: 4px; display: inline-flex; align-items: center;"
                  />
                </span>
              ),
              width: 100,
              align: "center",
              dataIndex: "dlpCommenced",
              scopedSlots: { customRender: "dlpCommenced" },
            },
            {
              title: "remark",
              width: 180,
              align: "center",
              ellipsis: true,
              dataIndex: "remark",
              scopedSlots: { customRender: "remark" },
            },
          ],
        },
      ],
      url: {
        list: "/eei/siteProgress/list",
        delete: "/eei/siteProgress/delete",
        getSubSystem: "/eei/siteProgress/getSubSystem",
        add: "/eei/siteProgress/add",
        edit: "/eei/siteProgress/edit",
      },
    };
  },
  methods: {
    getSubSystem() {
      getAction(this.url.getSubSystem).then((res) => {
        if (res.success) {
          this.subSystemList = res.result;
        }
      });
    },
    addSubSystem() {
      if (this.systemId == 0) {
        this.$message.warning("请选择系统");
        return;
      }
      var data = {
        systemId: this.systemId,
        projectId: this.projectId,
      };
      postAction(this.url.add, data).then((res) => {
        if (res.success) {
          this.systemId = 0;
          this.loadData(1);
          this.$message.success("添加成功");
        }
      });
    },
    handleEdit(record) {
      this.isEdit = true;
      this.editId = record.id;
      this.editItem = record;
    },
    handleCancel() {
      this.isEdit = false;
      this.editId = 0;
      this.editItem = {};
    },
    handleSave() {
      putAction(this.url.edit, this.editItem).then((res) => {
        if (res.success) {
          this.isEdit = false;
          this.editId = 0;
          this.editItem = {};
          this.loadData(1);
          this.$message.success("保存成功");
        }
      });
    },
    // 计算数组的累加和
    calculateCumulativeSum(arr) {
      return arr.reduce((acc, curr, index) => {
        if (index === 0) {
          acc.push(curr);
        } else {
          acc.push(acc[index - 1] + curr);
        }
        return acc;
      }, []);
    },

    // 在提交数据前处理
    handleSubmit() {
      // 假设 actAndFctCostDatas 是你的原始数组
      const originalData = this.actAndFctCostDatas;
      // 计算累加和
      const cumulativeData = this.calculateCumulativeSum(originalData);
      // 更新数据
      this.actAndFctCostDatas = cumulativeData;

      // 然后进行你的提交操作
      // ...
    },
  },
  created() {
    this.getSubSystem();
  },
};
</script>

<style lang="less" scoped>
/deep/ .blue-text-progress .ant-progress-text {
  color: #1890ff;
}
</style>

